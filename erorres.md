95   return serviceOrders.map(order => ({
                              ~~~~~

app/services/route-optimization.server.ts:285:24 - error TS7006: Parameter 'technician' implicitly has an 'any' type.

285   technicians.forEach((technician, index) => {
                           ~~~~~~~~~~

app/services/route-optimization.server.ts:285:36 - error TS7006: Parameter 'index' implicitly has an 'any' type.

285   technicians.forEach((technician, index) => {
                                       ~~~~~

app/services/search.server.ts:104:24 - error TS2339: Property 'id' does not exist on type 'Customer'.

104           id: customer.id,
                           ~~

app/services/search.server.ts:108:26 - error TS2339: Property 'id' does not exist on type 'Customer'.

108             id: customer.id,
                             ~~

app/services/search.server.ts:109:28 - error TS2339: Property 'name' does not exist on type 'Customer'.

109             name: customer.name,
                               ~~~~

app/services/search.server.ts:110:29 - error TS2339: Property 'email' does not exist on type 'Customer'.

110             email: customer.email,
                                ~~~~~

app/services/search.server.ts:111:29 - error TS2339: Property 'phone' does not exist on type 'Customer'.

111             phone: customer.phone,
                                ~~~~~

app/services/search.server.ts:112:31 - error TS2339: Property 'address' does not exist on type 'Customer'.

112             address: customer.address,
                                  ~~~~~~~

app/services/search.server.ts:113:28 - error TS2339: Property 'city' does not exist on type 'Customer'.

113             city: customer.city,
                               ~~~~

app/services/search.server.ts:114:34 - error TS2339: Property 'postalCode' does not exist on type 'Customer'.

114             postalCode: customer.postalCode,
                                     ~~~~~~~~~~

app/services/search.server.ts:115:31 - error TS2339: Property 'country' does not exist on type 'Customer'.

115             country: customer.country,
                                  ~~~~~~~

app/services/search.server.ts:124:22 - error TS2339: Property 'id' does not exist on type 'Device'.

124           id: device.id,
                         ~~

app/services/search.server.ts:128:24 - error TS2339: Property 'id' does not exist on type 'Device'.

128             id: device.id,
                           ~~

app/services/search.server.ts:129:26 - error TS2339: Property 'name' does not exist on type 'Device'.

129             name: device.name,
                             ~~~~

app/services/search.server.ts:130:27 - error TS2339: Property 'model' does not exist on type 'Device'.

130             model: device.model,
                              ~~~~~

app/services/search.server.ts:131:34 - error TS2339: Property 'serialNumber' does not exist on type 'Device'.

131             serialNumber: device.serialNumber,
                                     ~~~~~~~~~~~~

app/services/search.server.ts:132:34 - error TS2339: Property 'manufacturer' does not exist on type 'Device'.

132             manufacturer: device.manufacturer,
                                     ~~~~~~~~~~~~

app/services/search.server.ts:133:32 - error TS2339: Property 'customerId' does not exist on type 'Device'.

133             customerId: device.customerId,
                                   ~~~~~~~~~~

app/services/search.server.ts:134:34 - error TS2339: Property 'customer' does not exist on type 'Device'.

134             customerName: device.customer?.name,
                                     ~~~~~~~~

app/services/search.server.ts:143:21 - error TS2339: Property 'id' does not exist on type 'ServiceOrder'.

143           id: order.id,
                        ~~

app/services/search.server.ts:147:23 - error TS2339: Property 'id' does not exist on type 'ServiceOrder'.

147             id: order.id,
                          ~~

app/services/search.server.ts:148:26 - error TS2339: Property 'title' does not exist on type 'ServiceOrder'.

148             title: order.title,
                             ~~~~~

app/services/search.server.ts:149:32 - error TS2339: Property 'description' does not exist on type 'ServiceOrder'.

149             description: order.description,
                                   ~~~~~~~~~~~

app/services/search.server.ts:150:27 - error TS2339: Property 'status' does not exist on type 'ServiceOrder'.

150             status: order.status,
                              ~~~~~~

app/services/search.server.ts:151:31 - error TS2339: Property 'customerId' does not exist on type 'ServiceOrder'.

151             customerId: order.customerId,
                                  ~~~~~~~~~~

app/services/search.server.ts:152:33 - error TS2339: Property 'customer' does not exist on type 'ServiceOrder'.

152             customerName: order.customer?.name,
                                    ~~~~~~~~

app/services/search.server.ts:153:29 - error TS2339: Property 'deviceId' does not exist on type 'ServiceOrder'.

153             deviceId: order.deviceId,
                                ~~~~~~~~

app/services/search.server.ts:154:31 - error TS2339: Property 'device' does not exist on type 'ServiceOrder'.

154             deviceName: order.device?.name,
                                  ~~~~~~

app/services/search.server.ts:162:32 - error TS7006: Parameter 'note' implicitly has an 'any' type.

162         notesResponse.data.map(note => ({
                                   ~~~~

app/services/service-order.service.ts:2:57 - error TS2307: Cannot find module '@prisma/client' or its corresponding type declarations.

2 import type { ServiceOrder as PrismaServiceOrder } from "@prisma/client";
                                                          ~~~~~~~~~~~~~~~~

app/services/service-order.service.ts:203:25 - error TS2339: Property 'customerId' does not exist on type 'Omit<ServiceOrder, "id" | "createdAt" | "updatedAt">'.

203       where: { id: data.customerId, userId },
                            ~~~~~~~~~~

app/services/service-order.service.ts:215:14 - error TS2339: Property 'deviceId' does not exist on type 'Omit<ServiceOrder, "id" | "createdAt" | "updatedAt">'.

215     if (data.deviceId) {
                 ~~~~~~~~

app/services/service-order.service.ts:218:20 - error TS2339: Property 'deviceId' does not exist on type 'Omit<ServiceOrder, "id" | "createdAt" | "updatedAt">'.

218           id: data.deviceId,
                       ~~~~~~~~

app/services/service-order.service.ts:220:28 - error TS2339: Property 'customerId' does not exist on type 'Omit<ServiceOrder, "id" | "createdAt" | "updatedAt">'.

220           customerId: data.customerId,
                               ~~~~~~~~~~

app/services/service-order.service.ts:297:14 - error TS2339: Property 'customerId' does not exist on type 'Partial<Omit<ServiceOrder, "id" | "createdAt" | "updatedAt">>'.

297     if (data.customerId && data.customerId !== existingServiceOrder.customerId) {
                 ~~~~~~~~~~

app/services/service-order.service.ts:297:33 - error TS2339: Property 'customerId' does not exist on type 'Partial<Omit<ServiceOrder, "id" | "createdAt" | "updatedAt">>'.

297     if (data.customerId && data.customerId !== existingServiceOrder.customerId) {
                                    ~~~~~~~~~~

app/services/service-order.service.ts:299:27 - error TS2339: Property 'customerId' does not exist on type 'Partial<Omit<ServiceOrder, "id" | "createdAt" | "updatedAt">>'.

299         where: { id: data.customerId, userId },
                              ~~~~~~~~~~

app/services/service-order.service.ts:312:14 - error TS2339: Property 'deviceId' does not exist on type 'Partial<Omit<ServiceOrder, "id" | "createdAt" | "updatedAt">>'.

312     if (data.deviceId && data.deviceId !== existingServiceOrder.deviceId) {
                 ~~~~~~~~

app/services/service-order.service.ts:312:31 - error TS2339: Property 'deviceId' does not exist on type 'Partial<Omit<ServiceOrder, "id" | "createdAt" | "updatedAt">>'.

312     if (data.deviceId && data.deviceId !== existingServiceOrder.deviceId) {
                                  ~~~~~~~~

app/services/service-order.service.ts:313:31 - error TS2339: Property 'customerId' does not exist on type 'Partial<Omit<ServiceOrder, "id" | "createdAt" | "updatedAt">>'.

313       const customerId = data.customerId || existingServiceOrder.customerId;
                                  ~~~~~~~~~~

app/services/service-order.service.ts:317:20 - error TS2339: Property 'deviceId' does not exist on type 'Partial<Omit<ServiceOrder, "id" | "createdAt" | "updatedAt">>'.

317           id: data.deviceId,
                       ~~~~~~~~

app/services/service-order.service.ts:454:37 - error TS7006: Parameter 'order' implicitly has an 'any' type.

454       .map(id => serviceOrders.find(order => order.id === id))
                                        ~~~~~

app/services/sms.server.ts:84:3 - error TS6133: 'serviceOrderId' is declared but its value is never read.

84   serviceOrderId: string,
     ~~~~~~~~~~~~~~

app/services/visualization.service.ts:2:27 - error TS2307: Cannot find module '@prisma/client' or its corresponding type declarations.

2 import type { User } from "@prisma/client";
                            ~~~~~~~~~~~~~~~~

app/services/visualization.service.ts:80:31 - error TS7006: Parameter 'sum' implicitly has an 'any' type.

80     ? completedOrders.reduce((sum, order) => {
                                 ~~~

app/services/visualization.service.ts:80:36 - error TS7006: Parameter 'order' implicitly has an 'any' type.

80     ? completedOrders.reduce((sum, order) => {
                                      ~~~~~

app/session.server.ts:6:24 - error TS6133: 'isMfaVerified' is declared but its value is never read.

6 import { isMfaEnabled, isMfaVerified, setMfaVerified } from "~/services/mfa.server";
                         ~~~~~~~~~~~~~

app/utils/db-optimization.server.ts:8:1 - error TS6133: 'Prisma' is declared but its value is never read.

8 import type { Prisma } from "@prisma/client";
  ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

app/utils/db-optimization.server.ts:8:29 - error TS2307: Cannot find module '@prisma/client' or its corresponding type declarations.

8 import type { Prisma } from "@prisma/client";
                              ~~~~~~~~~~~~~~~~

app/utils/db-pool.server.ts:8:30 - error TS2307: Cannot find module '@prisma/client' or its corresponding type declarations.

8 import { PrismaClient } from "@prisma/client";
                               ~~~~~~~~~~~~~~~~

app/utils/db-pool.server.ts:105:22 - error TS2345: Argument of type 'unknown' is not assignable to parameter of type 'Error'.

105     captureException(error);
                         ~~~~~

app/utils/email.server.ts:13:7 - error TS6133: 'EMAIL_REPLY_TO' is declared but its value is never read.

13 const EMAIL_REPLY_TO = process.env.EMAIL_REPLY_TO || "<EMAIL>";
         ~~~~~~~~~~~~~~

app/utils/indexing.server.ts:9:8 - error TS2307: Cannot find module '~/services/search.supabase.server' or its corresponding type declarations.

9 } from "~/services/search.supabase.server";
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

app/utils/metrics.client.ts:188:52 - error TS2339: Property 'METRICS_ENDPOINT' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

188   if (typeof window !== 'undefined' && window.ENV?.METRICS_ENDPOINT) {
                                                       ~~~~~~~~~~~~~~~~

app/utils/metrics.client.ts:190:24 - error TS2339: Property 'METRICS_ENDPOINT' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

190       fetch(window.ENV.METRICS_ENDPOINT, {
                           ~~~~~~~~~~~~~~~~

app/utils/metrics.client.ts:243:12 - error TS2307: Cannot find module 'web-vitals' or its corresponding type declarations.

243     import('web-vitals').then(({ getFCP, getLCP, getFID, getCLS }) => {
               ~~~~~~~~~~~~

app/utils/metrics.client.ts:244:14 - error TS7006: Parameter 'metric' implicitly has an 'any' type.

244       getFCP(metric => recordHistogram(MetricName.PAGE_FIRST_CONTENTFUL_PAINT, metric.value));
                 ~~~~~~

app/utils/metrics.client.ts:245:14 - error TS7006: Parameter 'metric' implicitly has an 'any' type.

245       getLCP(metric => recordHistogram(MetricName.PAGE_LARGEST_CONTENTFUL_PAINT, metric.value));
                 ~~~~~~

app/utils/metrics.client.ts:246:14 - error TS7006: Parameter 'metric' implicitly has an 'any' type.

246       getFID(metric => recordHistogram(MetricName.PAGE_FIRST_INPUT_DELAY, metric.value));
                 ~~~~~~

app/utils/metrics.client.ts:247:14 - error TS7006: Parameter 'metric' implicitly has an 'any' type.

247       getCLS(metric => recordHistogram(MetricName.PAGE_CUMULATIVE_LAYOUT_SHIFT, metric.value * 1000)); // Convert to milliseconds
                 ~~~~~~

app/utils/monitoring.client.ts:25:52 - error TS2339: Property 'SENTRY_DSN' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

25   if (typeof window !== 'undefined' && window.ENV?.SENTRY_DSN && Sentry.init) {
                                                      ~~~~~~~~~~

app/utils/monitoring.client.ts:43:25 - error TS2339: Property 'SENTRY_DSN' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

43         dsn: window.ENV.SENTRY_DSN,
                           ~~~~~~~~~~

app/utils/monitoring.client.ts:45:33 - error TS2339: Property 'NODE_ENV' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

45         environment: window.ENV.NODE_ENV || 'production',
                                   ~~~~~~~~

app/utils/monitoring.client.ts:65:52 - error TS2339: Property 'SENTRY_DSN' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

65   if (typeof window !== 'undefined' && window.ENV?.SENTRY_DSN) {
                                                      ~~~~~~~~~~

app/utils/monitoring.client.ts:78:52 - error TS2339: Property 'SENTRY_DSN' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

78   if (typeof window !== 'undefined' && window.ENV?.SENTRY_DSN) {
                                                      ~~~~~~~~~~

app/utils/monitoring.client.ts:89:52 - error TS2339: Property 'SENTRY_DSN' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

89   if (typeof window !== 'undefined' && window.ENV?.SENTRY_DSN) {
                                                      ~~~~~~~~~~

app/utils/monitoring.client.ts:106:52 - error TS2339: Property 'SENTRY_DSN' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

106   if (typeof window !== 'undefined' && window.ENV?.SENTRY_DSN) {
                                                       ~~~~~~~~~~

app/utils/monitoring.client.ts:129:52 - error TS2339: Property 'SENTRY_DSN' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

129   if (typeof window !== 'undefined' && window.ENV?.SENTRY_DSN) {
                                                       ~~~~~~~~~~

app/utils/monitoring.client.ts:158:27 - error TS2339: Property 'SENTRY_DSN' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

158           if (window.ENV?.SENTRY_DSN) {
                              ~~~~~~~~~~

app/utils/monitoring.client.ts:173:31 - error TS2339: Property 'processingStart' does not exist on type 'PerformanceEntry'.

173             const fid = entry.processingStart - entry.startTime;
                                  ~~~~~~~~~~~~~~~

app/utils/monitoring.client.ts:177:29 - error TS2339: Property 'SENTRY_DSN' does not exist on type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }'.

177             if (window.ENV?.SENTRY_DSN) {
                                ~~~~~~~~~~

app/utils/monitoring.client.ts:195:5 - error TS2687: All declarations of 'ENV' must have identical modifiers.

195     ENV?: {
        ~~~

app/utils/monitoring.client.ts:195:5 - error TS2717: Subsequent property declarations must have the same type.  Property 'ENV' must be of type '{ STRIPE_PUBLISHABLE_KEY: string | undefined; }', but here has type '{ [key: string]: any; SENTRY_DSN?: string | undefined; NODE_ENV?: string | undefined; } | undefined'.

195     ENV?: {
        ~~~

  remix.env.d.ts:5:3
    5   ENV: {
        ~~~
    'ENV' was also declared here.

app/utils/monitoring.server.ts:77:56 - error TS2503: Cannot find namespace 'Sentry'.

77 export function captureMessage(message: string, level: Sentry.SeverityLevel = 'info') {
                                                          ~~~~~~

app/utils/offline-storage.ts:36:24 - error TS6133: 'event' is declared but its value is never read.

36     request.onerror = (event) => {
                          ~~~~~

app/utils/offline-storage.ts:40:26 - error TS6133: 'event' is declared but its value is never read.

40     request.onsuccess = (event) => {
                            ~~~~~

app/utils/offline-storage.ts:650:36 - error TS2769: No overload matches this call.
  Overload 1 of 2, '(query?: IDBValidKey | IDBKeyRange | null | undefined, count?: number | undefined): IDBRequest<any[]>', gave the following error.
    Argument of type 'false' is not assignable to parameter of type 'IDBValidKey | IDBKeyRange | null | undefined'.
  Overload 2 of 2, '(query?: IDBValidKey | IDBKeyRange | null | undefined, count?: number | undefined): IDBRequest<any[]>', gave the following error.
    Argument of type 'false' is not assignable to parameter of type 'IDBValidKey | IDBKeyRange | null | undefined'.

650       const request = index.getAll(false);
                                       ~~~~~


app/utils/security.server.ts:47:43 - error TS6133: 'request' is declared but its value is never read.

47 export function securityHeadersMiddleware({ request }: DataFunctionArgs) {
                                             ~~~~~~~~~~~

app/utils/supabase.ts:15:11 - error TS6133: 'data' is declared but its value is never read.

15   const { data, error } = await supabase.storage.from(bucket).upload(path, file, {
             ~~~~

app/utils/sync-manager.ts:8:3 - error TS6133: 'addToSyncQueue' is declared but its value is never read.

8   addToSyncQueue,
    ~~~~~~~~~~~~~~

app/utils/sync-manager.ts:9:3 - error TS6133: 'saveUserData' is declared but its value is never read.

9   saveUserData,
    ~~~~~~~~~~~~

app/utils/sync-manager.ts:10:3 - error TS6133: 'saveReferenceData' is declared but its value is never read.

10   saveReferenceData,
     ~~~~~~~~~~~~~~~~~

app/utils/sync-manager.ts:171:24 - error TS18046: 'order' is of type 'unknown'.

171         const isNew = !order.id || order.id.startsWith('offline_');
                           ~~~~~

app/utils/sync-manager.ts:171:36 - error TS18046: 'order' is of type 'unknown'.

171         const isNew = !order.id || order.id.startsWith('offline_');
                                       ~~~~~

app/utils/sync-manager.ts:188:60 - error TS18046: 'order' is of type 'unknown'.

188             await deleteOfflineData(STORES.SERVICE_ORDERS, order.offlineId);
                                                               ~~~~~

app/utils/sync-manager.ts:192:41 - error TS18046: 'order' is of type 'unknown'.

192             await updateRelatedEntities(order.offlineId, serverOrder.id, SyncType.SERVICE_ORDER);
                                            ~~~~~

app/utils/sync-manager.ts:199:66 - error TS18046: 'order' is of type 'unknown'.

199           const getResponse = await fetch(`/api/service-orders/${order.id}`);
                                                                     ~~~~~

app/utils/sync-manager.ts:213:19 - error TS18046: 'order' is of type 'unknown'.

213                   order.id,
                      ~~~~~

app/utils/sync-manager.ts:226:64 - error TS18046: 'order' is of type 'unknown'.

226                 await deleteOfflineData(STORES.SERVICE_ORDERS, order.offlineId);
                                                                   ~~~~~

app/utils/sync-manager.ts:236:75 - error TS18046: 'order' is of type 'unknown'.

236                 const updateResponse = await fetch(`/api/service-orders/${order.id}`, {
                                                                              ~~~~~

app/utils/sync-manager.ts:245:66 - error TS18046: 'order' is of type 'unknown'.

245                   await deleteOfflineData(STORES.SERVICE_ORDERS, order.offlineId);
                                                                     ~~~~~

app/utils/sync-manager.ts:256:71 - error TS18046: 'order' is of type 'unknown'.

256             const updateResponse = await fetch(`/api/service-orders/${order.id}`, {
                                                                          ~~~~~

app/utils/sync-manager.ts:265:62 - error TS18046: 'order' is of type 'unknown'.

265               await deleteOfflineData(STORES.SERVICE_ORDERS, order.offlineId);
                                                                 ~~~~~

app/utils/sync-manager.ts:372:29 - error TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ PENDING: number; SCHEDULED: number; IN_PROGRESS: number; ON_HOLD: number; COMPLETED: number; CANCELLED: number; }'.

372       const localPriority = statusPriority[localData.status] || 0;
                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

app/utils/sync-manager.ts:373:30 - error TS7053: Element implicitly has an 'any' type because expression of type 'any' can't be used to index type '{ PENDING: number; SCHEDULED: number; IN_PROGRESS: number; ON_HOLD: number; COMPLETED: number; CANCELLED: number; }'.

373       const serverPriority = statusPriority[serverData.status] || 0;
                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

app/utils/sync-manager.ts:399:13 - error TS18046: 'report' is of type 'unknown'.

399         if (report.serviceOrderId === offlineId) {
                ~~~~~~

app/utils/sync-manager.ts:400:11 - error TS18046: 'report' is of type 'unknown'.

400           report.serviceOrderId = serverId;
              ~~~~~~

app/utils/sync-manager.ts:401:59 - error TS2345: Argument of type 'unknown' is not assignable to parameter of type '{ offlineId: string; }'.

401           await updateOfflineData(STORES.SERVICE_REPORTS, report);
                                                              ~~~~~~

app/utils/sync-manager.ts:410:13 - error TS18046: 'order' is of type 'unknown'.

410         if (order.customerId === offlineId) {
                ~~~~~

app/utils/sync-manager.ts:411:11 - error TS18046: 'order' is of type 'unknown'.

411           order.customerId = serverId;
              ~~~~~

app/utils/sync-manager.ts:412:58 - error TS2345: Argument of type 'unknown' is not assignable to parameter of type '{ offlineId: string; }'.

412           await updateOfflineData(STORES.SERVICE_ORDERS, order);
                                                             ~~~~~

app/utils/sync-manager.ts:417:13 - error TS18046: 'device' is of type 'unknown'.

417         if (device.customerId === offlineId) {
                ~~~~~~

app/utils/sync-manager.ts:418:11 - error TS18046: 'device' is of type 'unknown'.

418           device.customerId = serverId;
              ~~~~~~

app/utils/sync-manager.ts:419:51 - error TS2345: Argument of type 'unknown' is not assignable to parameter of type '{ offlineId: string; }'.

419           await updateOfflineData(STORES.DEVICES, device);
                                                      ~~~~~~

app/utils/sync-manager.ts:478:3 - error TS6133: 'onStatusUpdate' is declared but its value is never read.

478   onStatusUpdate?: (update: SyncStatusUpdate) => void
      ~~~~~~~~~~~~~~

app/utils/sync-manager.ts:488:3 - error TS6133: 'onStatusUpdate' is declared but its value is never read.

488   onStatusUpdate?: (update: SyncStatusUpdate) => void
      ~~~~~~~~~~~~~~

prisma/seed.ts:1:30 - error TS2307: Cannot find module '@prisma/client' or its corresponding type declarations.

1 import { PrismaClient } from "@prisma/client";
                               ~~~~~~~~~~~~~~~~

scripts/production-readiness/backup-recovery.ts:8:7 - error TS6133: 'supabase' is declared but its value is never read.

8 const supabase = createClient(
        ~~~~~~~~

scripts/production-readiness/db-performance-optimization.ts:1:30 - error TS2307: Cannot find module '@prisma/client' or its corresponding type declarations.

1 import { PrismaClient } from '@prisma/client';
                               ~~~~~~~~~~~~~~~~

scripts/production-readiness/db-performance-optimization.ts:229:11 - error TS6133: 'cachedLoaderExample' is declared but its value is never read.

229     const cachedLoaderExample = `
              ~~~~~~~~~~~~~~~~~~~

scripts/production-readiness/load-testing.ts:77:27 - error TS7006: Parameter 'workflow' implicitly has an 'any' type.

77 function generateK6Script(workflow, concurrentUsers) {
                             ~~~~~~~~

scripts/production-readiness/load-testing.ts:77:37 - error TS7006: Parameter 'concurrentUsers' implicitly has an 'any' type.

77 function generateK6Script(workflow, concurrentUsers) {
                                       ~~~~~~~~~~~~~~~

scripts/production-readiness/load-testing.ts:193:48 - error TS18046: 'error' is of type 'unknown'.

193         console.error(`  Error running test: ${error.message}`);
                                                   ~~~~~

scripts/production-readiness/load-testing.ts:235:26 - error TS18047: 'b' is possibly 'null'.

235   results.sort((a, b) => b.p95 - a.p95);
                             ~

scripts/production-readiness/load-testing.ts:235:34 - error TS18047: 'a' is possibly 'null'.

235   results.sort((a, b) => b.p95 - a.p95);
                                     ~

scripts/production-readiness/load-testing.ts:246:28 - error TS18047: 'result' is possibly 'null'.

246       const workflowName = result.file.split('-')[0];
                               ~~~~~~

scripts/production-readiness/load-testing.ts:248:74 - error TS18047: 'result' is possibly 'null'.

248       console.log(`\n${i + 1}. Optimize ${workflowName} workflow (p95: ${result.p95}ms):`);
                                                                             ~~~~~~

scripts/production-readiness/setup-monitoring-dashboard.ts:8:7 - error TS6133: 'supabase' is declared but its value is never read.

8 const supabase = createClient(
        ~~~~~~~~

supabase/functions/scheduled-tasks/index.ts:5:23 - error TS2307: Cannot find module 'https://deno.land/std@0.168.0/http/server.ts' or its corresponding type declarations.

5 import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

supabase/functions/scheduled-tasks/index.ts:6:30 - error TS2307: Cannot find module 'https://esm.sh/@supabase/supabase-js@2' or its corresponding type declarations.

6 import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

supabase/functions/scheduled-tasks/index.ts:8:3 - error TS6133: 'corsHeaders' is declared but its value is never read.

8   corsHeaders,
    ~~~~~~~~~~~

supabase/functions/scheduled-tasks/index.ts:14:8 - error TS2307: Cannot find module '../config.ts' or its corresponding type declarations.

14 } from "../config.ts";
          ~~~~~~~~~~~~~~

supabase/functions/scheduled-tasks/index.ts:16:14 - error TS7006: Parameter 'req' implicitly has an 'any' type.

16 serve(async (req) => {
                ~~~

supabase/functions/scheduled-tasks/index.ts:27:7 - error TS2304: Cannot find name 'Deno'.

27       Deno.env.get("SUPABASE_URL") ?? "",
         ~~~~

supabase/functions/scheduled-tasks/index.ts:28:7 - error TS2304: Cannot find name 'Deno'.

28       Deno.env.get("SUPABASE_ANON_KEY") ?? "",
         ~~~~

supabase/functions/scheduled-tasks/index.ts:59:26 - error TS18046: 'error' is of type 'unknown'.

59     return errorResponse(error.message, 500);
                            ~~~~~

supabase/functions/scheduled-tasks/index.ts:63:35 - error TS7006: Parameter 'supabase' implicitly has an 'any' type.

63 async function handleCalendarSync(supabase) {
                                     ~~~~~~~~

supabase/functions/scheduled-tasks/index.ts:103:39 - error TS7006: Parameter 'supabase' implicitly has an 'any' type.

103 async function handleMaintenanceCheck(supabase) {
                                          ~~~~~~~~

supabase/functions/scheduled-tasks/index.ts:120:45 - error TS7006: Parameter 'device' implicitly has an 'any' type.

120   const serviceOrdersToCreate = devices.map(device => ({
                                                ~~~~~~

supabase/functions/scheduled-tasks/index.ts:173:42 - error TS7006: Parameter 'supabase' implicitly has an 'any' type.

173 async function handleNotificationSending(supabase) {
                                             ~~~~~~~~

supabase/functions/scheduled-tasks/index.ts:227:34 - error TS7006: Parameter 'supabase' implicitly has an 'any' type.

227 async function handleDataCleanup(supabase) {
                                     ~~~~~~~~

supabase/functions/scheduled-tasks/index.ts:241:15 - error TS6133: 'data' is declared but its value is never read.

241       const { data, error, count } = await supabase
                  ~~~~

supabase/functions/scheduled-tasks/index.ts:252:22 - error TS2339: Property 'events' does not exist on type '{}'.

252       cleanupResults.events = count || 0;
                         ~~~~~~

supabase/functions/scheduled-tasks/index.ts:267:24 - error TS2339: Property 'logs' does not exist on type '{}'.

267         cleanupResults.logs = 0;
                           ~~~~

supabase/functions/scheduled-tasks/index.ts:269:24 - error TS2339: Property 'logs' does not exist on type '{}'.

269         cleanupResults.logs = count || 0;
                           ~~~~

supabase/functions/webhooks/index.ts:5:23 - error TS2307: Cannot find module 'https://deno.land/std@0.168.0/http/server.ts' or its corresponding type declarations.

5 import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

supabase/functions/webhooks/index.ts:6:30 - error TS2307: Cannot find module 'https://esm.sh/@supabase/supabase-js@2' or its corresponding type declarations.

6 import { createClient } from "https://esm.sh/@supabase/supabase-js@2";
                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

supabase/functions/webhooks/index.ts:14:8 - error TS2307: Cannot find module '../config.ts' or its corresponding type declarations.

14 } from "../config.ts";
          ~~~~~~~~~~~~~~

supabase/functions/webhooks/index.ts:16:14 - error TS7006: Parameter 'req' implicitly has an 'any' type.

16 serve(async (req) => {
                ~~~

supabase/functions/webhooks/index.ts:24:7 - error TS2304: Cannot find name 'Deno'.

24       Deno.env.get("SUPABASE_URL") ?? "",
         ~~~~

supabase/functions/webhooks/index.ts:25:7 - error TS2304: Cannot find name 'Deno'.

25       Deno.env.get("SUPABASE_ANON_KEY") ?? "",
         ~~~~

supabase/functions/webhooks/index.ts:70:26 - error TS18046: 'error' is of type 'unknown'.

70     return errorResponse(error.message, 500);
                            ~~~~~

supabase/functions/webhooks/index.ts:74:36 - error TS7006: Parameter 'req' implicitly has an 'any' type.

74 async function handleStripeWebhook(req, supabase) {
                                      ~~~

supabase/functions/webhooks/index.ts:74:41 - error TS7006: Parameter 'supabase' implicitly has an 'any' type.

74 async function handleStripeWebhook(req, supabase) {
                                           ~~~~~~~~

supabase/functions/webhooks/index.ts:114:32 - error TS7006: Parameter 'supabase' implicitly has an 'any' type.

114 async function logWebhookEvent(supabase, source, eventType, eventId) {
                                   ~~~~~~~~

supabase/functions/webhooks/index.ts:114:42 - error TS7006: Parameter 'source' implicitly has an 'any' type.

114 async function logWebhookEvent(supabase, source, eventType, eventId) {
                                             ~~~~~~

supabase/functions/webhooks/index.ts:114:50 - error TS7006: Parameter 'eventType' implicitly has an 'any' type.

114 async function logWebhookEvent(supabase, source, eventType, eventId) {
                                                     ~~~~~~~~~

supabase/functions/webhooks/index.ts:114:61 - error TS7006: Parameter 'eventId' implicitly has an 'any' type.

114 async function logWebhookEvent(supabase, source, eventType, eventId) {
                                                                ~~~~~~~

supabase/functions/webhooks/index.ts:131:41 - error TS7006: Parameter 'paymentIntent' implicitly has an 'any' type.

131 async function processSuccessfulPayment(paymentIntent, supabase) {
                                            ~~~~~~~~~~~~~

supabase/functions/webhooks/index.ts:131:56 - error TS7006: Parameter 'supabase' implicitly has an 'any' type.

131 async function processSuccessfulPayment(paymentIntent, supabase) {
                                                           ~~~~~~~~

supabase/functions/webhooks/index.ts:172:35 - error TS7006: Parameter 'invoice' implicitly has an 'any' type.

172 async function processInvoicePaid(invoice, supabase) {
                                      ~~~~~~~

supabase/functions/webhooks/index.ts:172:44 - error TS7006: Parameter 'supabase' implicitly has an 'any' type.

172 async function processInvoicePaid(invoice, supabase) {
                                               ~~~~~~~~

supabase/functions/webhooks/index.ts:204:42 - error TS7006: Parameter 'subscription' implicitly has an 'any' type.

204 async function processSubscriptionChange(subscription, supabase) {
                                             ~~~~~~~~~~~~

supabase/functions/webhooks/index.ts:204:56 - error TS7006: Parameter 'supabase' implicitly has an 'any' type.

204 async function processSubscriptionChange(subscription, supabase) {
                                                           ~~~~~~~~

supabase/functions/webhooks/index.ts:224:37 - error TS7006: Parameter 'req' implicitly has an 'any' type.

224 async function handleOutlookWebhook(req, supabase) {
                                        ~~~

supabase/functions/webhooks/index.ts:224:42 - error TS7006: Parameter 'supabase' implicitly has an 'any' type.

224 async function handleOutlookWebhook(req, supabase) {
                                             ~~~~~~~~

supabase/functions/webhooks/index.ts:283:45 - error TS7006: Parameter 'req' implicitly has an 'any' type.

283 async function handleDeviceTelemetryWebhook(req, supabase) {
                                                ~~~

supabase/functions/webhooks/index.ts:283:50 - error TS7006: Parameter 'supabase' implicitly has an 'any' type.

283 async function handleDeviceTelemetryWebhook(req, supabase) {
                                                     ~~~~~~~~


Found 1410 errors in 224 files.

Errors  Files
     4  app/components/CalendarEntryServiceOrderButton.tsx:2
     1  app/components/RealTimeUpdates.tsx:75
     2  app/components/agent/AgentChat.tsx:8
     3  app/components/agent/AgentDashboard.tsx:8
     1  app/components/atoms/charts/bar-chart.tsx:1
     2  app/components/atoms/charts/gauge-chart.tsx:1
     1  app/components/atoms/charts/line-chart.tsx:1
     2  app/components/atoms/charts/pie-chart.tsx:1
     3  app/components/atoms/service-status-indicator.tsx:1
     6  app/components/dynamic/CustomFieldsSection.tsx:1
     3  app/components/dynamic/DynamicField.tsx:1
     3  app/components/dynamic/DynamicForm.tsx:1
     4  app/components/dynamic/DynamicView.tsx:1
    16  app/components/dynamic/ViewCustomizer.tsx:1
     1  app/components/hocs/with-progressive-enhancement.tsx:4
     5  app/components/index.ts:14
     1  app/components/mode-toggle.tsx:6
     2  app/components/molecules/CustomerSelect.tsx:1
     1  app/components/molecules/OfferCard.tsx:1
     1  app/components/molecules/OfferStatusBadge.tsx:1
     3  app/components/molecules/dashboard-header.tsx:1
     3  app/components/molecules/date-range-picker.tsx:1
     3  app/components/molecules/device-health-indicator.tsx:5
     1  app/components/molecules/device/prediction-chart.tsx:77
     1  app/components/molecules/file-upload/file-upload.tsx:2
     1  app/components/molecules/notifications/notification-badge.tsx:1
     1  app/components/molecules/notifications/notification-item.tsx:1
     7  app/components/molecules/notifications/notification-preferences.tsx:29
     3  app/components/molecules/quick-actions.tsx:1
     4  app/components/molecules/service-order-card-compact.tsx:5
     1  app/components/molecules/timeline-event.tsx:3
     4  app/components/organisms/DataExplorationVisualization.tsx:1
     3  app/components/organisms/OfferForm.tsx:1
     5  app/components/organisms/ServiceOrderFlowVisualization.tsx:1
     1  app/components/organisms/conflict-resolution.tsx:1
     2  app/components/organisms/customer-communication-center.tsx:166
     3  app/components/organisms/dashboard-widget-grid.tsx:2
     3  app/components/organisms/dashboard-widget.tsx:1
     3  app/components/organisms/device/DeviceTelemetryDashboard.tsx:1
     3  app/components/organisms/device/PredictiveMaintenancePanel.tsx:6
    10  app/components/organisms/enhanced-predictive-maintenance.tsx:8
     1  app/components/organisms/error-boundary.tsx:1
     2  app/components/organisms/header.tsx:1
     8  app/components/organisms/maintenance-dashboard.tsx:15
     4  app/components/organisms/notification-center.tsx:1
     6  app/components/organisms/predictive-maintenance-summary.tsx:6
     1  app/components/organisms/service-history-timeline.tsx:6
     2  app/components/organisms/sync-status.tsx:1
    12  app/components/pages/technician-mobile-dashboard.tsx:9
     1  app/components/payment/PaymentMethodSelector.tsx:2
     1  app/components/pwa-update-prompt.tsx:2
     1  app/components/templates/main-layout.tsx:1
     9  app/components/templates/printable-invoice.tsx:1
     3  app/components/templates/printable-service-report.tsx:1
     1  app/components/theme-provider.tsx:75
     1  app/components/ui/avatar.tsx:1
     2  app/components/ui/calendar.tsx:54
     1  app/components/ui/chart.tsx:1
     1  app/components/ui/date-picker.tsx:1
     2  app/components/ui/dialog.tsx:12
     4  app/components/ui/error-boundary.test.tsx:104
     1  app/contexts/service-availability.tsx:75
     2  app/db.server.ts:1
     1  app/entry.server.tsx:64
     8  app/entry.worker.ts:3
     3  app/graphql/schema.ts:2
     2  app/lib/agent-protocol-client/client.ts:56
     1  app/middleware/auth.server.ts:2
     1  app/middleware/logging.server.test.ts:4
     1  app/middleware/logging.server.ts:16
    14  app/models/custom-fields.server.ts:2
    16  app/models/inventory.server.ts:1
     2  app/models/metadata.server.ts:2
     1  app/models/note.server.ts:1
     3  app/models/notification.server.ts:2
     2  app/models/report.server.ts:1
     2  app/models/user-settings.server.ts:1
     2  app/models/user.server.ts:1
     1  app/models/view-definitions.server.ts:2
     1  app/models/workflow-definitions.server.ts:2
     3  app/root.tsx:17
     4  app/routes/admin.calendar-processing.tsx:9
     4  app/routes/admin.events.tsx:16
     1  app/routes/admin.settings.tsx:13
     1  app/routes/api.agent.chat.message.tsx:39
     1  app/routes/api.agent.chat.status.tsx:54
     3  app/routes/api.agent.dashboard.tsx:9
     2  app/routes/api.events.ts:24
     1  app/routes/api.notifications.tsx:43
     6  app/routes/api.sync.ts:293
     3  app/routes/augment-supabase-demo.tsx:159
    23  app/routes/calendar.$calendarEntryId.edit.tsx:65
    20  app/routes/calendar.$calendarEntryId.tsx:2
    18  app/routes/calendar._index.tsx:3
    14  app/routes/calendar.new.tsx:2
     1  app/routes/calendar.oauth.callback.tsx:1
    10  app/routes/customer-communication.tsx:10
     2  app/routes/customer-portal.devices._index.tsx:7
     4  app/routes/customer-portal.invoices.$invoiceId.tsx:5
     1  app/routes/customer-portal.invoices._index.tsx:32
    19  app/routes/customer-portal.request-service.tsx:5
     2  app/routes/customer-portal.service-orders.$serviceOrderId.tsx:168
     6  app/routes/customer-portal.service-orders._index.tsx:32
     7  app/routes/customers.$customerId.custom-fields.tsx:8
    12  app/routes/customers.$customerId.edit.tsx:108
    25  app/routes/customers.$customerId.tsx:52
    16  app/routes/customers._index.tsx:101
     3  app/routes/customers.new.tsx:63
     1  app/routes/customers.tsx:4
    10  app/routes/dashboard._index.tsx:2
     7  app/routes/dashboard.customize.tsx:1
    27  app/routes/dashboard.tsx:12
    19  app/routes/devices.$deviceId.edit.tsx:11
     8  app/routes/devices.$deviceId.predictions.tsx:46
    11  app/routes/devices.$deviceId.telemetry.tsx:55
    40  app/routes/devices.$deviceId.tsx:8
    40  app/routes/devices._index.tsx:147
    10  app/routes/devices.new.tsx:11
     2  app/routes/graphql.tsx:6
     5  app/routes/health.tsx:10
     1  app/routes/healthcheck.tsx:16
     5  app/routes/inventory._index.tsx:8
     3  app/routes/inventory.advanced.tsx:3
   107  app/routes/inventory.optimization.tsx:8
     6  app/routes/inventory.parts.new.tsx:9
    25  app/routes/inventory.reports.tsx:8
    18  app/routes/inventory.scanner.tsx:9
    18  app/routes/inventory.transactions.tsx:9
     1  app/routes/inventory.tsx:61
    10  app/routes/inventory.visualmap.tsx:6
     4  app/routes/invoices.$invoiceId.pay.tsx:45
     4  app/routes/invoices.$invoiceId.tsx:256
     2  app/routes/invoices._index.tsx:3
     2  app/routes/invoices.new.tsx:119
     2  app/routes/login.tsx:13
     1  app/routes/manifest[.]webmanifest.tsx:9
     3  app/routes/notes.new.tsx:8
     1  app/routes/notes.tsx:48
     5  app/routes/notifications.tsx:14
     1  app/routes/offer-templates._index.tsx:43
     3  app/routes/offers.$offerId.tsx:197
     1  app/routes/offers._index.tsx:106
     1  app/routes/offline.tsx:98
     3  app/routes/reports/_index.tsx:5
    12  app/routes/reports/financial.tsx:3
     2  app/routes/search._index.tsx:44
     8  app/routes/search.index-management.tsx:48
    26  app/routes/service-orders.$serviceOrderId.edit.tsx:45
    53  app/routes/service-orders.$serviceOrderId.tsx:9
    21  app/routes/service-orders._index.tsx:10
    15  app/routes/service-orders.new.tsx:35
     1  app/routes/service-reports.$reportId.tsx:318
     2  app/routes/service-reports._index.tsx:3
     8  app/routes/service-reports.new.tsx:6
     5  app/routes/settings.company.tsx:1
    26  app/routes/settings.customization.tsx:215
    11  app/routes/settings.mfa.tsx:97
     2  app/routes/settings.notifications.tsx:24
     1  app/routes/settings.preview-invoice.tsx:134
     1  app/routes/settings.preview-report.tsx:130
     1  app/routes/settings.service-status.tsx:19
     1  app/routes/settings.sync-status.tsx:14
     3  app/routes/settings.tsx:11
     2  app/routes/sitemap[.]xml.tsx:46
     3  app/routes/supabase-demo.tsx:6
     5  app/routes/supabase-test.tsx:6
     9  app/routes/technicians.route-planner.tsx:9
     2  app/routes/visualizations._index.tsx:2
    10  app/service-worker.ts:18
     1  app/services/agent-protocol.server.ts:128
     7  app/services/augment-supabase.server.ts:43
     8  app/services/auto-service-order.server.ts:2
     1  app/services/automation.service.ts:2
     1  app/services/calendar-semantic.server.ts:2
     1  app/services/calendar.service.ts:2
     4  app/services/communication.service.ts:2
     1  app/services/company-settings.server.ts:2
     3  app/services/customer-portal.server.ts:2
     4  app/services/customer.service.test.ts:2
     8  app/services/customer.service.ts:2
     2  app/services/database-sync.server.ts:7
     1  app/services/db.server.ts:87
     6  app/services/device.service.ts:2
     4  app/services/eventBus.server.ts:82
     5  app/services/eventHandlers.server.ts:144
     6  app/services/inventory-alerts.server.ts:3
     2  app/services/mfa.server.ts:4
     2  app/services/microsoft-graph.server.ts:9
     1  app/services/note.service.ts:278
     3  app/services/notification.server.ts:16
    11  app/services/ocr/ocr.server.ts:2
     2  app/services/offer-template.service.ts:2
     6  app/services/offer-variant.service.ts:2
     9  app/services/offer.service.ts:2
    15  app/services/offlineSync.client.ts:15
     6  app/services/outlook-calendar.server.ts:6
     4  app/services/payment.server.ts:3
     2  app/services/predictive-maintenance.server.ts:2
     3  app/services/qdrant.server.ts:2
    19  app/services/report.server.ts:2
     3  app/services/route-optimization.server.ts:95
    27  app/services/search.server.ts:104
    13  app/services/service-order.service.ts:2
     1  app/services/sms.server.ts:84
     3  app/services/visualization.service.ts:2
     1  app/session.server.ts:6
     2  app/utils/db-optimization.server.ts:8
     2  app/utils/db-pool.server.ts:8
     1  app/utils/email.server.ts:13
     1  app/utils/indexing.server.ts:9
     7  app/utils/metrics.client.ts:188
    13  app/utils/monitoring.client.ts:25
     1  app/utils/monitoring.server.ts:77
     3  app/utils/offline-storage.ts:36
     1  app/utils/security.server.ts:47
     1  app/utils/supabase.ts:15
    27  app/utils/sync-manager.ts:8
     1  prisma/seed.ts:1
     1  scripts/production-readiness/backup-recovery.ts:8
     2  scripts/production-readiness/db-performance-optimization.ts:1
     7  scripts/production-readiness/load-testing.ts:77
     1  scripts/production-readiness/setup-monitoring-dashboard.ts:8
    17  supabase/functions/scheduled-tasks/index.ts:5
    23  supabase/functions/webhooks/index.ts:5
koldbringer@DESKTOP-7PB97N3:~/HVAC/hvac-remix$ 